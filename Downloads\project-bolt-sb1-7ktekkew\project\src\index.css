@tailwind base;
@tailwind components;
@tailwind utilities;

/* Typography Base Styles */
@layer base {
  html {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "pnum" 1, "tnum" 0,
      "onum" 1, "lnum" 0, "dlig" 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    @apply font-body text-body text-charcoal;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }

  /* Heading defaults */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "dlig" 1;
  }

  /* Improved currency symbol rendering */
  .currency {
    font-feature-settings: "kern" 1, "liga" 1, "tnum" 1, "lnum" 1;
    font-variant-numeric: tabular-nums;
  }

  /* Better button text */
  button {
    @apply font-accent;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  /* Navigation text optimization */
  nav {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }
}

/* Component-specific typography */
@layer components {
  .text-luxury {
    @apply font-heading font-semibold tracking-tight text-primary-800;
    font-feature-settings: "kern" 1, "liga" 1, "dlig" 1;
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 1px 2px rgba(249, 115, 22, 0.1);
  }

  .text-premium {
    @apply font-accent font-medium text-secondary-600;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  .text-price {
    @apply font-accent font-bold currency text-secondary-700;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-location {
    @apply font-body font-medium text-charcoal-700;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  /* Luxury Card Styles */
  .card-luxury {
    @apply bg-gradient-to-br from-accent-50 to-accent-100 shadow-card-premium border border-accent-200/50;
    backdrop-filter: blur(10px);
  }

  .card-luxury:hover {
    @apply shadow-luxury-lg;
    transform: translateY(-2px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Premium Button Styles */
  .btn-luxury {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-btn;
    @apply hover:shadow-btn-hover hover:from-primary-500 hover:to-primary-600;
    @apply transition-all duration-300 ease-out;
    @apply border border-primary-500/20;
  }

  .btn-luxury:hover {
    transform: translateY(-1px);
  }

  .btn-secondary-luxury {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-700 text-white shadow-btn-gold;
    @apply hover:shadow-btn-gold-hover hover:from-secondary-500 hover:to-secondary-600;
    @apply transition-all duration-300 ease-out;
    @apply border border-secondary-500/30;
  }

  .btn-secondary-luxury:hover {
    transform: translateY(-1px);
  }

  .btn-outline-luxury {
    @apply border-2 border-primary-600 text-primary-600 bg-transparent;
    @apply hover:bg-primary-600 hover:text-white;
    @apply transition-all duration-300 ease-out;
    @apply shadow-card;
  }

  .btn-outline-luxury:hover {
    @apply shadow-btn;
    transform: translateY(-1px);
  }

  /* Glass Effect Components */
  .glass-effect {
    @apply bg-glass backdrop-blur-sm border border-accent-200/30 shadow-glass;
  }

  .glass-dark {
    @apply bg-glass-dark backdrop-blur-sm border border-primary-700/30 shadow-glass-dark;
  }

  /* Section Backgrounds */
  .section-luxury {
    @apply bg-gradient-to-br from-accent-50 via-accent-100 to-accent-50;
    background-image: radial-gradient(
        circle at 25% 25%,
        rgba(212, 175, 55, 0.05) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 75% 75%,
        rgba(30, 41, 59, 0.03) 0%,
        transparent 50%
      );
  }

  .section-premium {
    @apply bg-gradient-to-br from-primary-900 via-primary-800 to-primary-900;
    background-image: radial-gradient(
        circle at 20% 80%,
        rgba(212, 175, 55, 0.1) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(212, 175, 55, 0.05) 0%,
        transparent 50%
      );
  }

  /* Luxury Dividers */
  .divider-luxury {
    @apply h-px bg-gradient-to-r from-transparent via-secondary-400 to-transparent;
  }

  .divider-premium {
    @apply h-0.5 bg-gradient-to-r from-transparent via-secondary-500 to-transparent;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
  }

  /* Enhanced gradient text effects */
  .text-gradient-primary {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-luxury {
    background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #f97316 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-gold {
    background: linear-gradient(135deg, #d4af37 0%, #f7e98e 50%, #d4af37 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Advanced glass morphism */
  .glass-premium {
    @apply backdrop-blur-xl bg-white/10 border border-white/20;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-dark-premium {
    @apply backdrop-blur-xl bg-black/10 border border-white/10;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }

  /* Shimmer loading effect */
  .shimmer-effect {
    @apply relative overflow-hidden;
  }

  .shimmer-effect::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%];
    animation: shimmer 2s infinite;
  }

  /* Floating animation variants */
  .floating-slow {
    animation: float 8s ease-in-out infinite;
  }

  .floating-fast {
    animation: float 4s ease-in-out infinite;
  }

  .floating-delayed {
    animation: float 6s ease-in-out infinite;
    animation-delay: -2s;
  }

  /* Premium hover effects */
  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    @apply -translate-y-2 shadow-luxury-lg;
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out;
  }

  .hover-glow:hover {
    @apply shadow-glow;
  }

  /* Parallax container */
  .parallax-container {
    @apply relative overflow-hidden;
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  .parallax-element {
    @apply absolute inset-0;
    transform: translateZ(-1px) scale(2);
  }

  /* Luxury form styles */
  .input-luxury {
    @apply w-full px-4 py-3 border border-accent-300 rounded-lg bg-white/80 backdrop-blur-sm;
    @apply focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 focus:outline-none;
    @apply transition-all duration-300 ease-out;
  }

  .input-luxury:focus {
    @apply shadow-luxury;
  }

  .textarea-luxury {
    @apply w-full px-4 py-3 border border-accent-300 rounded-lg bg-white/80 backdrop-blur-sm;
    @apply focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 focus:outline-none;
    @apply transition-all duration-300 ease-out resize-none;
    min-height: 120px;
  }

  .textarea-luxury:focus {
    @apply shadow-luxury;
  }

  /* Navigation enhancements */
  .nav-luxury {
    @apply backdrop-blur-xl bg-white/90 border-b border-white/20;
    backdrop-filter: blur(20px);
  }

  .nav-dark {
    @apply backdrop-blur-xl bg-black/80 border-b border-white/10;
    backdrop-filter: blur(20px);
  }

  /* Section spacing utilities */
  .section-padding {
    @apply py-16 sm:py-20 lg:py-24;
  }

  .section-padding-sm {
    @apply py-12 sm:py-16 lg:py-20;
  }

  .section-padding-lg {
    @apply py-20 sm:py-24 lg:py-32;
  }

  /* Container utilities */
  .container-luxury {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-narrow {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-wide {
    @apply max-w-8xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}
